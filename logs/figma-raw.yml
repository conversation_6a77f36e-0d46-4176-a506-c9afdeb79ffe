name: 🌰 MuchSkills MASTER
lastModified: '2025-06-02T17:15:17Z'
thumbnailUrl: >-
  https://s3-alpha.figma.com/thumbnails/d4d886b7-ece4-4236-b50c-e20a6df3d348?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQ4GOSFWCWP5TY7EM%2F20250601%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250601T120000Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=7abc45b467e9c004a58d542441506438e79736ff3a13cafb07ad883d711f8dbd
version: '2225106068527584532'
role: viewer
editorType: figma
linkAccess: inherit
nodes:
  '20084:67033':
    document:
      id: '20084:67033'
      name: Toggle
      type: INSTANCE
      scrollBehavior: SCROLLS
      componentId: '41:3534'
      componentProperties:
        'On':
          value: 'True'
          type: VARIANT
          boundVariables: {}
      overrides: []
      children:
        - id: I20084:67033;877:6469
          name: Rectangle 122
          type: RECTANGLE
          scrollBehavior: SCROLLS
          blendMode: PASS_THROUGH
          fills:
            - blendMode: NORMAL
              type: SOLID
              color:
                r: 0.9490196108818054
                g: 0.9490196108818054
                b: 0.9490196108818054
                a: 1
          strokes:
            - blendMode: NORMAL
              type: SOLID
              color:
                r: 0.686274528503418
                g: 0.686274528503418
                b: 0.686274528503418
                a: 1
          strokeWeight: 1
          strokeAlign: INSIDE
          styles:
            fill: '1:107'
            stroke: '1:232'
          cornerRadius: 100
          cornerSmoothing: 0
          absoluteBoundingBox:
            x: 6632
            'y': -19222
            width: 40
            height: 24
          absoluteRenderBounds:
            x: 6632
            'y': -19222
            width: 40
            height: 24
          constraints:
            vertical: SCALE
            horizontal: SCALE
          effects: []
          interactions: []
        - id: I20084:67033;877:6470
          name: Ellipse 143
          type: ELLIPSE
          scrollBehavior: SCROLLS
          blendMode: PASS_THROUGH
          fills:
            - blendMode: NORMAL
              type: SOLID
              color:
                r: 0.2980392277240753
                g: 0.9176470637321472
                b: 0.6941176652908325
                a: 1
          strokes:
            - blendMode: NORMAL
              type: SOLID
              color:
                r: 0.2705882489681244
                g: 0.7137255072593689
                b: 0.5529412031173706
                a: 1
          strokeWeight: 1
          strokeAlign: INSIDE
          styles:
            fill: '1:226'
            stroke: '1:105'
          absoluteBoundingBox:
            x: 6652
            'y': -19218
            width: 16
            height: 16
          absoluteRenderBounds:
            x: 6652
            'y': -19218
            width: 16
            height: 16
          constraints:
            vertical: SCALE
            horizontal: SCALE
          effects: []
          arcData:
            startingAngle: 0
            endingAngle: 6.2831854820251465
            innerRadius: 0
          interactions: []
      blendMode: PASS_THROUGH
      clipsContent: false
      background: []
      fills: []
      strokes: []
      strokeWeight: 1
      strokeAlign: INSIDE
      backgroundColor:
        r: 0
        g: 0
        b: 0
        a: 0
      absoluteBoundingBox:
        x: 6632
        'y': -19222
        width: 40
        height: 24
      absoluteRenderBounds:
        x: 6632
        'y': -19222
        width: 40
        height: 24
      constraints:
        vertical: TOP
        horizontal: LEFT
      layoutAlign: INHERIT
      layoutGrow: 0
      layoutSizingHorizontal: FIXED
      layoutSizingVertical: FIXED
      effects: []
      interactions: []
    components:
      '41:3534':
        key: c48df6d38ed993d4e5ada443331367f0f8c3d597
        name: On=True
        description: ''
        remote: true
        componentSetId: '41:3533'
        documentationLinks: []
    componentSets:
      '41:3533':
        key: 2a21909aedbc8c0056eb56a6a0e6f2fb259c77db
        name: Toggle
        description: ''
        remote: true
    schemaVersion: 0
    styles:
      '1:107':
        key: b3dffc5274d58c14d370acb1506f21005ecef60e
        name: msGray-6
        styleType: FILL
        remote: true
        description: ''
      '1:232':
        key: a9cb5d57b372c3076f94b1f8c199898e51a0096a
        name: msGray-4
        styleType: FILL
        remote: true
        description: ''
      '1:226':
        key: 5713d0a3099ae5bf5f98a07a50d27d096e72fecf
        name: msGreen-3
        styleType: FILL
        remote: true
        description: ''
      '1:105':
        key: ef49a13e3f1c77fe5560a6c025064d9ea429c880
        name: msGreen-2
        styleType: FILL
        remote: true
        description: ''
