name: 🌰 MuchSkills MASTER
lastModified: '2025-06-02T17:15:17Z'
thumbnailUrl: >-
  https://s3-alpha.figma.com/thumbnails/d4d886b7-ece4-4236-b50c-e20a6df3d348?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAQ4GOSFWCWP5TY7EM%2F20250601%2Fus-west-2%2Fs3%2Faws4_request&X-Amz-Date=20250601T120000Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=7abc45b467e9c004a58d542441506438e79736ff3a13cafb07ad883d711f8dbd
nodes:
  - id: '20084:67033'
    name: Toggle
    type: INSTANCE
    componentId: '41:3534'
    componentProperties:
      'On':
        value: 'True'
        type: VARIANT
    layout: layout_V96EXW
    children:
      - id: I20084:67033;877:6469
        name: Rectangle 122
        type: RECTANGLE
        fills: fill_FIN7PS
        strokes: stroke_5BJY3Y
        layout: layout_1L1CIB
        borderRadius: 100px
      - id: I20084:67033;877:6470
        name: Ellipse 143
        type: ELLIPSE
        fills: fill_PMO0U0
        strokes: stroke_X5RF8U
        layout: layout_XVV8S3
components:
  '41:3534':
    id: '41:3534'
    key: c48df6d38ed993d4e5ada443331367f0f8c3d597
    name: On=True
    componentSetId: '41:3533'
componentSets:
  '41:3533':
    id: '41:3533'
    key: 2a21909aedbc8c0056eb56a6a0e6f2fb259c77db
    name: Toggle
    description: ''
globalVars:
  styles:
    layout_V96EXW:
      mode: none
      sizing:
        horizontal: fixed
        vertical: fixed
      dimensions:
        width: 40
        height: 24
    fill_FIN7PS:
      - '#F2F2F2'
    stroke_5BJY3Y:
      colors:
        - '#AFAFAF'
      strokeWeight: 1px
    layout_1L1CIB:
      mode: none
      locationRelativeToParent:
        x: 0
        'y': 0
      dimensions:
        width: 40
        height: 24
    fill_PMO0U0:
      - '#4CEAB1'
    stroke_X5RF8U:
      colors:
        - '#45B68D'
      strokeWeight: 1px
    layout_XVV8S3:
      mode: none
      locationRelativeToParent:
        x: 20
        'y': 4
      dimensions:
        width: 16
        height: 16
