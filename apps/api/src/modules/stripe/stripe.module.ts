import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';

import { Plan, PlanSchema } from './plan.schema';
import { StripeWebhookService } from './stripe-webhook.service';
import { StripeController } from './stripe.controller';
import { StripeService } from './stripe.service';
import { OrganizationModule } from '../organization/organization.module';
import { UsersModule } from '../users/users.module';

@Global()
@Module({
  imports: [
    ConfigModule,
    OrganizationModule,
    UsersModule,
    MongooseModule.forFeature([{ name: Plan.name, schema: PlanSchema }]),
  ],
  providers: [StripeService, StripeWebhookService],
  controllers: [StripeController],
  exports: [StripeService, StripeWebhookService],
})
export class StripeModule {}
