import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, SchemaTypes } from 'mongoose';

function validateNumericLimit(fieldName: string) {
  return {
    validator: function (v: number) {
      return typeof v === 'number' && (v >= 0 || v === Infinity);
    },
    message: (props: { value: number }) =>
      `${props.value} is not a valid ${fieldName}! It must be a non-negative number or Infinity.`,
  };
}

export type PlanDocument = Plan & Document;

@Schema({ timestamps: true })
export class Plan {
  @Prop({ required: true, unique: true })
  name: string;

  @Prop()
  description?: string;

  @Prop({
    type: Number, // Changed from SchemaTypes.Mixed
    required: true,
    validate: validateNumericLimit('profile count'),
  })
  profiles: number;

  @Prop({
    type: Number, // Changed from SchemaTypes.Mixed
    required: true,
    validate: validateNumericLimit('max CVs count'),
  })
  maxCVs: number;

  @Prop({
    type: Number,
    required: true,
    validate: validateNumericLimit('user count'),
  })
  users: number;

  @Prop()
  aiGeneratedCVs?: boolean;

  @Prop()
  cvDatabase?: boolean;

  @Prop()
  skillsIntegration?: string;

  @Prop({ type: SchemaTypes.Mixed })
  skillsSearchAndMatching?: boolean | string;

  @Prop()
  dedicatedSupport?: boolean;

  @Prop()
  apiIntegrations?: boolean;

  @Prop()
  ssoSamlScim?: boolean;

  @Prop()
  sla?: boolean;

  @Prop({ required: true, unique: true })
  stripe_price_id: string;
}

export const PlanSchema = SchemaFactory.createForClass(Plan);
