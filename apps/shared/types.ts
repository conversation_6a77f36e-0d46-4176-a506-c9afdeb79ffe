import { Types } from 'mongoose';
import Stripe from 'stripe';

import { SignUpDto } from './dto/auth/sign-up.dto';

//Enums

export enum MetadataTier {
  FREE = 'free',
  PAID = 'paid',
  CONTACT_SALES = 'contact_sales',
}

export enum EmailTemplate {
  SIGNUP_CONFIRMATION = 'd-5795581425b749e0a050379201b19cb7',
  RESET_PASSWORD = 'd-2385786f51e84a4ba48e2f51f25e9e95',
  INVITATION_SENT = 'd-91092abe51fc4f068242f769a60701de',
}

export enum ErrorCode {
  EMAIL_TAKEN = 'Email already taken',
  LINK_INVALID = 'Link invalid',
  NAME_REQUIRED = 'Name is required',
  NOT_FOUND = 'Not found',
  PERMISSION_DENIED = 'Permission denied',
  UNEXPECTED_ERROR = 'unexpected_error',
  EMAIL_REQUIRED = 'email_required',
}

export enum SectionType {
  personalInfo = 'personalInfo',
  aboutMe = 'aboutMe',
  workHistory = 'workHistory',
  education = 'education',
  certifications = 'certifications',
  skills = 'skills',
  languages = 'languages',
  customSection = 'customSection',
}

export enum UserRole {
  OWNER = 'OWNER',
  ADMIN = 'ADMIN',
  MEMBER = 'MEMBER',
}

export enum InviteStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  EXPIRED = 'expired',
}

export enum TimeRange {
  hour = 'hour',
  day = 'day',
  month = 'month',
}

export const timeRangeData = {
  [TimeRange.hour]: { name: 'Hour' },
  [TimeRange.day]: { name: 'Day' },
  [TimeRange.month]: { name: 'Month' },
};

export enum Currency {
  usd = 'usd',
  eur = 'eur',
  gbp = 'gbp',
}

export const currencyData = {
  [Currency.usd]: { name: 'USD', sign: '$' },
  [Currency.eur]: { name: 'EUR', sign: '€' },
  [Currency.gbp]: { name: 'GBP', sign: '£' },
};

export enum CvStatus {
  draft = 'draft',
  active = 'active',
}

export const cvStatusData = {
  [CvStatus.draft]: {
    name: 'Draft',
    bgColor: 'bg-msGray-6',
    textColor: 'text-msGray-3',
  },
  [CvStatus.active]: {
    name: 'Active',
    bgColor: 'bg-msGreen-4',
    textColor: 'text-msGreen-1',
  },
};

export enum Template {
  europass = 'europass',
  federalUsa = 'federalUsa',
}

export const templateData = {
  [Template.europass]: 'Europass CV',
  [Template.federalUsa]: 'Federal Resume (USA)',
};

export enum UserType {
  employee = 'employee',
  consultant = 'consultant',
  candidate = 'candidate',
}

export const userTypeData = {
  [UserType.employee]: { name: 'Employee', color: 'bg-msGreen-4' },
  [UserType.consultant]: { name: 'Consultant', color: 'bg-msYellow-2' },
  [UserType.candidate]: { name: 'Candidate', color: 'bg-msBlue-3' },
};

export enum MemberSource {
  muchskills = 'muchskills',
  linkedin = 'linkedin',
  cvinventory = 'cvinventory',
}

export const memberSourceData = {
  [MemberSource.muchskills]: {
    name: 'MuchSkills',
    icon: '/icons/muchskills.svg',
    color: 'bg-msBlack',
  },
  [MemberSource.linkedin]: {
    name: 'LinkedIn',
    icon: '/icons/linkedin.svg',
    color: 'bg-[#2867B2]',
  },
  [MemberSource.cvinventory]: {
    name: 'Add member',
    icon: '/icons/plus.svg',
    color: 'bg-msBlack',
  },
};

export enum GroupFilter {
  all = 'all',
  draft = 'draft',
}

export const groupFilterData = {
  [GroupFilter.all]: { name: 'All' },
  [GroupFilter.draft]: { name: 'Draft' },
};

export enum CvProfileFilter {
  all = 'all',
  hasCvProf = 'hasCvProf',
  hasNoCvProf = 'hasNoCvProf',
}

export const cvProfileFilterData = {
  [CvProfileFilter.all]: { name: 'All' },
  [CvProfileFilter.hasCvProf]: { name: 'Has CV profile' },
  [CvProfileFilter.hasNoCvProf]: { name: 'Has no CV profile' },
};

export enum AppPlanTier {
  FREE = 'free',
  BASIC = 'basic',
  PRO = 'pro',
  BUSINESS = 'business',
  ENTERPRISE = 'enterprise', // Added enterprise
}

export interface PlanFeature {
  name: string;
  value: string;
}
//Interfaces
export interface Plan {
  name: string;
  price: string;
  description: string;
  buttonText: string;
  buttonType: 'primary' | 'secondary';
  features: PlanFeature[];
  priceId?: string; // Stripe price ID for Upgrade
  tier?: string; // Added for sorting and consistent identification
}
export interface ResolvedInvite {
  itemName: string;
  organizationName: string;
  organizationPhoto: string;
  inviteItemCreated: boolean;
  errorCode?: ErrorCode;
}

export type SignUpData = SignUpDto & ResolvedInvite;

export interface CostRate {
  currency: Currency;
  amount: number;
  timeRange: TimeRange;
}

export interface Social {
  name: string;
  link: string;
}

export interface Member {
  _id: string;
  avatar?: string;
  firstName: string;
  lastName: string;
  email: string;
  location?: string;
  telephone?: string;
  currentPosition?: string;
  currentLevel?: string;
  yearsOfExperience?: number;
  languages: string[];
  type?: UserType;
  clients: string[];
  costRate?: CostRate;
  socials: Social[];
  source: MemberSource;
  sourceId?: string;
  cvs: Cv[];
  competencesAmount?: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface CvPreferences {
  title: string;
  maxPages?: number;
  role?: string;
  level?: string;
  rate?: number;
  currency?: Currency;
  timeRange?: TimeRange;
  client?: string;
  link?: string;
  contractStart?: Date;
  contractEnd?: Date;
  autoRenewal?: boolean;
  leastExperience?: number;
  maxExperience?: number;
  skills?: string[];
  description?: string;
}

export interface Cv {
  _id: string;
  template: Template;
  status: CvStatus;
  preferences: CvPreferences;
  sections: CvSections;
  createdAt: Date;
  updatedAt: Date;
}

export interface CvSections {
  personalInfo: {
    order: number;
    title: string;
    data: PersonalInfoData;
  };
  aboutMe: {
    order: number;
    title: string;
    data: AboutMeData;
  };
  workHistory: {
    order: number;
    title: string;
    data: WorkHistoryRecord[];
  };
  education: {
    order: number;
    title: string;
    data: EducationRecord[];
  };
  certifications: {
    order: number;
    title: string;
    data: CertificationsData;
  };
  skills: {
    order: number;
    title: string;
    data: SkillsData;
  };
  languages: {
    order: number;
    title: string;
    data: LanguagesData;
  };
  customSections: {
    order: number;
    title: string;
    data: CustomSectionData;
  }[];
}
export interface Paging {
  page: number;
  itemsPerPage: number;
}

export interface SectionBase {
  id: string;
  mandatory?: boolean;
  order: number;
  title: string;
}

//

export interface PersonalInfoData {
  firstName: string;
  lastName: string;
  jobTitle: { value: string; active: boolean };
  location: { value: string; active: boolean };
  nationality: { value: string; active: boolean };
  email: { value: string; active: boolean };
  telephone: { value: string; active: boolean };
  socials: { title: string; value: string; active: boolean }[];
}

export interface AboutMeData {
  aboutMe: string;
}

export interface WorkHistoryRecord {
  active: boolean;
  organizationName: string;
  roleTitle: string;
  roleDescription: string;
  startDate: Date | null;
  endDate: Date | null;
  isCurrent: boolean;
}

export interface EducationRecord {
  active: boolean;
  schoolName: string;
  degree: string;
  description: string;
  startDate: Date | null;
  endDate: Date | null;
}

export interface CertificationsData {
  certifications: string;
}

export interface SkillsData {
  skills: string;
}

export interface LanguagesData {
  languages: string[];
}

export interface CustomSectionData {
  richText: string;
}

export type SectionData =
  | PersonalInfoData
  | AboutMeData
  | WorkHistoryRecord[]
  | EducationRecord[]
  | CertificationsData
  | SkillsData
  | LanguagesData
  | CustomSectionData;

export interface Section extends SectionBase {
  data: SectionData;
}

export interface PaginatedMembers {
  members: Member[];
  totalMembers: number;
}

export interface MuchskillsMember {
  email: string;
  name: string;
  image: string;
  title: string;
  location: string;
  competencesCount: number;
  cvsCount: number;
  profileExist: boolean;
  localId?: string | Types.ObjectId;
}

export interface PaginatedMuchskillsMembers {
  members: MuchskillsMember[];
  total: number;
}

export interface Organization {
  _id: string;
  name: string;
  photo?: string;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
  planStatus?:
    | 'trialing'
    | 'active'
    | 'past_due'
    | 'canceled'
    | 'unpaid'
    | 'incomplete'
    | 'incomplete_expired'
    | 'paused'
    | 'free';
  planId?: string;
  currentPeriodEnds?: Date;
  trialEndsAt?: Date;
  muchskillsIntegration?: MuchskillsIntegration;
}

export interface CustomStripeProductMetadata {
  id: string; // Plan identifier like 'free', 'pro', 'business', 'enterprise'
  app: 'cv'; // App identifier
  max_cv?: string; // String representation of the number
  profiles?: string; // String representation of the number
  users?: string; // String representation of the number
  // Add any other direct feature keys you expect from metadata
  [key: string]: string | undefined; // Allow other string keys for flexibility
}

export interface ApiStripeProduct
  extends Omit<Stripe.Product, 'default_price' | 'metadata'> {
  default_price: Stripe.Price | null; // API MUST expand this to a Price object or null
  metadata: CustomStripeProductMetadata;
  prices: Stripe.Price[]; // Added prices array
}

export interface MuchskillsIntegration {
  token: string;
  connected: boolean;
  lastSync?: Date;
}
