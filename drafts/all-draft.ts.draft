// Organization interface based on data stored in localStorage
interface Organization {
  id: string;
  name: string;
  photo?: string;
}

{
  "name": "api",
  "$schema": "../../node_modules/nx/schemas/project-schema.json",
  "sourceRoot": "apps/api/src",
  "projectType": "application",
  "targets": {
    "build": {
      "executor": "@nx/webpack:webpack",
      "outputs": ["{options.outputPath}"],
      "defaultConfiguration": "production",
      "options": {
        "target": "node",
        "compiler": "tsc",
        "outputPath": "dist/apps/api",
        "main": "apps/api/src/main.ts",
        "tsConfig": "apps/api/tsconfig.app.json",
        "assets": ["apps/api/src/assets"],
        "isolatedConfig": true,
        "webpackConfig": "apps/api/webpack.config.js"
      },
      "configurations": {
        "development": {},
        "production": {}
      }
    },
    "serve": {
      "executor": "@nx/js:node",
      "defaultConfiguration": "development",
      "options": {
        "buildTarget": "api:build"
      },
      "configurations": {
        "development": {
          "buildTarget": "api:build:development"
        },
        "production": {
          "buildTarget": "api:build:production"
        }
      }
    },
    "lint": {
      "executor": "@nx/linter:eslint",
      "outputs": ["{options.outputFile}"],
      "options": {
        "lintFilePatterns": ["apps/api/**/*.ts"]
      }
    },
    "test": {
      "executor": "@nx/jest:jest",
      "outputs": ["{workspaceRoot}/coverage/{projectRoot}"],
      "options": {
        "jestConfig": "apps/api/jest.config.ts",
        "passWithNoTests": true
      },
      "configurations": {
        "ci": {
          "ci": true,
          "codeCoverage": true
        }
      }
    }
  },
  "tags": []
}

"metadata": {
  "app": "cv",
  "inherits_from": "<some_product_id>",
  "contact_sales": "true" // if not business or enterprise, remove this
  "features_json": "{\"Custom contract and terms\": true, \"Dedicated onboarding\": true}"
}

[
  {
    "name": "Free Plan",
    "price": "Free",
    "description": "Ideal for tryouts, small agencies",
    "profiles": 20,
    "maxCVs": 20,
    "users": 5,
    "aiGeneratedCVs": "Yes (limited)",
    "cvDatabase": true,
    "skillsIntegration": "MuchSkills",
    "skillsSearchAndMatching": "Yes (limited)"
  },
  {
    "name": "CV Inventory Business",
    "price": "$399.00/month",
    "description": null,
    "profiles": 500,
    "maxCVs": 100,
    "users": 20
  },
  {
    "name": "CV Inventory Pro",
    "price": "$199.00/month",
    "description": null,
    "profiles": 100,
    "maxCVs": 50,
    "users": 5
  },
  {
    "name": "Enterprise",
    "price": "Custom pricing",
    "description": "Global complex orgs, needing full control",
    "profiles": "Unlimited",
    "maxCVs": "Unlimited",
    "users": "Unlimited",
    "skillsSearchAndMatching": true,
    "dedicatedSupport": true,
    "apiIntegrations": true,
    "ssoSamlScim": true,
    "sla": true
  }
]

