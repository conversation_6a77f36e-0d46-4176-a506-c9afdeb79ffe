/**
 * @format
 */

export default {
  mongodb: {
    url:
      process.env.MONGO_URI ||
      'mongodb://' +
        (process.env.NODE_ENV === 'development'
          ? 'localhost:27017'
          : 'ms-mongo'),
    databaseName: process.env.DATABASE_NAME || 'cv-builder',
    options: { useUnifiedTopology: true },
  },
  migrationsDir: 'api/migrations',
  changelogCollectionName: 'changelog',
  migrationFileExtension: '.ts',
};
